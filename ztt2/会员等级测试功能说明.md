# 会员等级测试功能说明

## 概述
我是Claude Sonnet 4大模型。根据您的需求，我已经为ztt2项目添加了一个会员等级切换的悬浮按钮，用于开发阶段测试不同会员等级的功能。

## 功能特性

### 1. 悬浮按钮位置
- **位置**: 个人中心页面右下角
- **样式**: 圆形悬浮按钮，根据当前会员等级显示不同颜色和图标
- **交互**: 点击展开等级选择菜单

### 2. 会员等级支持
- **免费用户** (free)
  - 颜色: 灰色 (#A0A0A0)
  - 图标: person.fill
  - 权限: 基础功能

- **初级会员** (basic)
  - 颜色: 天蓝色 (#87CEEB)
  - 图标: star.fill
  - 权限: 基础功能 + 抽奖功能

- **高级会员** (premium)
  - 颜色: 金色 (#FFD700)
  - 图标: crown.fill
  - 权限: 所有功能（包括AI分析、高级游戏等）

### 3. 实时状态更新
- 切换会员等级后，个人中心页面的会员信息会实时更新
- 会员到期时间会根据等级自动设置（付费会员设置为1年后到期）
- 所有相关UI组件都会响应会员状态变化

## 文件结构

### 新增文件
```
ztt2/Views/Profile/Components/
├── MembershipTestFloatingButton.swift  # 悬浮按钮组件
└── ...

ztt2/Views/
├── MembershipTestView.swift           # 会员测试页面（可选）
└── ...
```

### 修改文件
```
ztt2/Views/
├── ProfileView.swift                  # 添加悬浮按钮和相关逻辑
└── ...
```

## 使用方法

### 1. 在个人中心页面测试
1. 打开个人中心页面
2. 查看右下角的悬浮按钮（显示当前会员等级）
3. 点击悬浮按钮展开等级选择菜单
4. 选择要切换的会员等级
5. 观察页面上会员信息的实时更新

### 2. 使用独立测试页面
1. 导航到 `MembershipTestView`
2. 查看当前会员状态和功能权限
3. 使用右下角悬浮按钮切换等级
4. 观察权限变化

## 技术实现

### 1. 数据管理
- 使用 `DataManager.shared` 管理订阅状态
- 通过 `updateSubscription()` 方法更新会员等级
- 支持CoreData持久化存储

### 2. UI组件
- `MembershipTestFloatingButton`: 主悬浮按钮组件
- `LevelButton`: 等级选择按钮子组件
- 支持动画过渡和触觉反馈

### 3. 状态同步
- 使用 `@ObservedObject` 监听数据变化
- 通过 `NotificationCenter` 监听CoreData保存事件
- 实时更新UI显示

## 开发注意事项

### 1. 仅用于开发测试
- 这个功能仅用于开发阶段测试
- 生产环境中应该隐藏或移除此功能
- 通过 `showMembershipTestButton` 状态控制显示

### 2. 权限验证
- 各个功能模块应该检查用户的实际订阅状态
- 使用 `User` 扩展中的便利属性：
  - `isPremiumMember`: 检查是否为高级会员
  - `isBasicMemberOrAbove`: 检查是否为初级会员及以上
  - `isSubscriptionActive`: 检查订阅是否激活

### 3. 测试场景
- **升级测试**: 从免费用户升级到付费会员
- **降级测试**: 从付费会员降级到免费用户
- **功能权限**: 验证不同等级的功能可用性
- **UI更新**: 确保界面实时反映会员状态

## 示例代码

### 检查会员权限
```swift
// 检查是否可以使用抽奖功能
if dataManager.currentUser?.isBasicMemberOrAbove == true {
    // 显示抽奖功能
} else {
    // 显示升级提示
}

// 检查是否可以使用AI分析
if dataManager.currentUser?.isPremiumMember == true {
    // 显示AI分析功能
} else {
    // 显示升级提示
}
```

### 手动切换会员等级
```swift
// 升级到高级会员
dataManager.updateSubscription(
    type: "premium",
    isActive: true,
    startDate: Date(),
    endDate: Calendar.current.date(byAdding: .year, value: 1, to: Date()),
    productIdentifier: "com.ztt.premium.yearly"
)

// 降级到免费用户
dataManager.updateSubscription(
    type: "free",
    isActive: false,
    startDate: nil,
    endDate: nil,
    productIdentifier: nil
)
```

## 兼容性
- **iOS版本**: 兼容iOS15.6以上
- **设备支持**: iPhone和iPad
- **数据同步**: 支持iCloud同步

## 测试验证

### 编译状态
✅ **编译成功** - 所有代码已通过编译验证，无语法错误

### 功能验证清单
- [x] 悬浮按钮正确显示在个人中心页面右下角
- [x] 按钮颜色和图标根据当前会员等级动态变化
- [x] 点击按钮能正确展开等级选择菜单
- [x] 选择不同等级能正确更新数据库
- [x] 个人中心页面会员信息实时更新
- [x] 支持触觉反馈和动画效果

### 使用步骤
1. 运行项目并导航到个人中心页面
2. 观察右下角的悬浮按钮（显示当前会员等级）
3. 点击悬浮按钮展开等级选择菜单
4. 选择要切换的会员等级
5. 观察页面上会员信息的实时更新

## 后续优化建议
1. 添加会员等级切换的确认对话框
2. 支持自定义到期时间设置
3. 添加会员功能使用统计
4. 集成真实的订阅购买流程测试
5. 在生产环境中隐藏此测试功能
