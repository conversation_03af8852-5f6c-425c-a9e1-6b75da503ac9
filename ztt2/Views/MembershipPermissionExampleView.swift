//
//  MembershipPermissionExampleView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/3.
//

import SwiftUI

/**
 * 会员权限检查示例页面
 * 演示如何在不同页面中检查和使用会员权限
 */
struct MembershipPermissionExampleView: View {
    
    @ObservedObject private var dataManager = DataManager.shared
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 标题
                    Text("会员权限检查示例")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .padding(.top)
                    
                    // 当前会员状态
                    currentMembershipStatusCard
                    
                    // 功能权限示例
                    functionalPermissionExamples
                    
                    // 代码示例
                    codeExamples
                    
                    Spacer(minLength: 100)
                }
                .padding()
            }
            .navigationTitle("权限示例")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
    
    // MARK: - 当前会员状态卡片
    private var currentMembershipStatusCard: some View {
        VStack(spacing: 12) {
            Text("当前会员状态")
                .font(.headline)
                .foregroundColor(.secondary)
            
            if let user = dataManager.currentUser {
                VStack(spacing: 8) {
                    HStack {
                        Text("会员等级:")
                        Spacer()
                        Text(getMembershipLevelName(user.subscriptionType))
                            .fontWeight(.semibold)
                            .foregroundColor(getMembershipColor(user.subscriptionType))
                    }
                    
                    HStack {
                        Text("订阅状态:")
                        Spacer()
                        Text(user.isSubscriptionActive ? "激活" : "未激活")
                            .fontWeight(.semibold)
                            .foregroundColor(user.isSubscriptionActive ? .green : .red)
                    }
                    
                    if let subscription = user.subscription, let endDate = subscription.endDate {
                        HStack {
                            Text("到期时间:")
                            Spacer()
                            Text(formatDate(endDate))
                                .fontWeight(.medium)
                                .foregroundColor(.secondary)
                        }
                    }
                }
            } else {
                Text("未找到用户信息")
                    .foregroundColor(.red)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.gray.opacity(0.1))
        )
    }
    
    // MARK: - 功能权限示例
    private var functionalPermissionExamples: some View {
        VStack(spacing: 16) {
            Text("功能权限检查示例")
                .font(.headline)
                .foregroundColor(.secondary)
            
            VStack(spacing: 12) {
                // 基础功能（所有用户）
                PermissionExampleRow(
                    title: "基础积分功能",
                    description: "所有用户都可以使用",
                    hasPermission: true,
                    requiredLevel: "免费用户"
                )
                
                // 抽奖功能（初级会员及以上）
                PermissionExampleRow(
                    title: "抽奖功能",
                    description: "大转盘抽奖",
                    hasPermission: dataManager.currentUser?.isBasicMemberOrAbove ?? false,
                    requiredLevel: "初级会员"
                )
                
                // AI分析功能（仅高级会员）
                PermissionExampleRow(
                    title: "AI分析报告",
                    description: "智能分析成长数据",
                    hasPermission: dataManager.currentUser?.isPremiumMember ?? false,
                    requiredLevel: "高级会员"
                )
                
                // 高级游戏（仅高级会员）
                PermissionExampleRow(
                    title: "高级游戏",
                    description: "盲盒、刮刮卡等",
                    hasPermission: dataManager.currentUser?.isPremiumMember ?? false,
                    requiredLevel: "高级会员"
                )
            }
        }
    }
    
    // MARK: - 代码示例
    private var codeExamples: some View {
        VStack(spacing: 16) {
            Text("代码示例")
                .font(.headline)
                .foregroundColor(.secondary)
            
            VStack(alignment: .leading, spacing: 12) {
                Text("检查会员权限的代码示例:")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                CodeExampleBlock(
                    title: "检查是否为高级会员",
                    code: """
if dataManager.currentUser?.isPremiumMember == true {
    // 显示高级功能
    showAIAnalysis()
} else {
    // 显示升级提示
    showUpgradePrompt()
}
"""
                )
                
                CodeExampleBlock(
                    title: "检查是否为付费会员",
                    code: """
if dataManager.currentUser?.isBasicMemberOrAbove == true {
    // 显示付费功能
    showLotteryFeature()
} else {
    // 显示免费功能限制
    showFreeUserLimitation()
}
"""
                )
                
                CodeExampleBlock(
                    title: "检查订阅状态",
                    code: """
guard let user = dataManager.currentUser,
      user.isSubscriptionActive else {
    // 订阅未激活
    return
}
// 继续执行付费功能
"""
                )
            }
        }
    }
    
    // MARK: - Helper Methods
    
    private func getMembershipLevelName(_ level: String) -> String {
        switch level {
        case "premium":
            return "高级会员"
        case "basic":
            return "初级会员"
        default:
            return "免费用户"
        }
    }
    
    private func getMembershipColor(_ level: String) -> Color {
        switch level {
        case "premium":
            return Color(hex: "#FFD700")
        case "basic":
            return Color(hex: "#87CEEB")
        default:
            return Color(hex: "#A0A0A0")
        }
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: date)
    }
}

// MARK: - 权限示例行组件
struct PermissionExampleRow: View {
    let title: String
    let description: String
    let hasPermission: Bool
    let requiredLevel: String
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.primary)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Text("需要: \(requiredLevel)")
                    .font(.caption2)
                    .foregroundColor(.blue)
            }
            
            Spacer()
            
            VStack(spacing: 4) {
                Image(systemName: hasPermission ? "checkmark.circle.fill" : "xmark.circle.fill")
                    .foregroundColor(hasPermission ? .green : .red)
                    .font(.system(size: 20))
                
                Text(hasPermission ? "可用" : "不可用")
                    .font(.caption2)
                    .fontWeight(.medium)
                    .foregroundColor(hasPermission ? .green : .red)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color.gray.opacity(0.05))
        )
    }
}

// MARK: - 代码示例块组件
struct CodeExampleBlock: View {
    let title: String
    let code: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.blue)
            
            Text(code)
                .font(.system(.caption, design: .monospaced))
                .foregroundColor(.primary)
                .padding(12)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.gray.opacity(0.1))
                )
        }
    }
}

// MARK: - Preview
#Preview {
    MembershipPermissionExampleView()
}
