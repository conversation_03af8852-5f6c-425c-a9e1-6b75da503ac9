//
//  MembershipTestView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/3.
//

import SwiftUI

/**
 * 会员等级测试页面
 * 用于测试会员等级切换功能和权限验证
 */
struct MembershipTestView: View {
    
    @ObservedObject private var dataManager = DataManager.shared
    @State private var currentLevel = "free"
    @State private var showTestResults = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                // 标题
                Text("会员等级测试")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .padding(.top, 20)
                
                // 当前状态显示
                VStack(spacing: 15) {
                    Text("当前会员状态")
                        .font(.headline)
                        .foregroundColor(.secondary)
                    
                    VStack(spacing: 8) {
                        Text(getLevelDisplayName(currentLevel))
                            .font(.title2)
                            .fontWeight(.semibold)
                            .foregroundColor(getLevelColor(currentLevel))
                        
                        if let user = dataManager.currentUser {
                            Text("订阅状态: \(user.isSubscriptionActive ? "激活" : "未激活")")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.gray.opacity(0.1))
                    )
                }
                
                // 功能权限测试
                VStack(spacing: 15) {
                    Text("功能权限测试")
                        .font(.headline)
                        .foregroundColor(.secondary)
                    
                    VStack(spacing: 10) {
                        PermissionRow(
                            title: "基础积分功能",
                            hasPermission: true,
                            description: "所有用户可用"
                        )
                        
                        PermissionRow(
                            title: "抽奖功能",
                            hasPermission: currentLevel == "basic" || currentLevel == "premium",
                            description: "初级会员及以上"
                        )
                        
                        PermissionRow(
                            title: "AI分析报告",
                            hasPermission: currentLevel == "premium",
                            description: "仅高级会员"
                        )
                        
                        PermissionRow(
                            title: "高级游戏",
                            hasPermission: currentLevel == "premium",
                            description: "仅高级会员"
                        )
                    }
                }
                
                Spacer()
                
                // 测试说明
                VStack(spacing: 8) {
                    Text("测试说明")
                        .font(.headline)
                        .foregroundColor(.secondary)
                    
                    Text("使用右下角的悬浮按钮切换会员等级，观察功能权限的变化")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                }
                .padding(.bottom, 20)
            }
            .padding()
            .navigationTitle("会员测试")
            .navigationBarTitleDisplayMode(.inline)
        }
        .onAppear {
            updateCurrentLevel()
        }
        .onReceive(NotificationCenter.default.publisher(for: .NSManagedObjectContextDidSave)) { _ in
            updateCurrentLevel()
        }
        .overlay(
            // 悬浮按钮
            VStack {
                Spacer()
                HStack {
                    Spacer()
                    MembershipTestFloatingButton(
                        currentLevel: currentLevel,
                        onLevelChanged: { newLevel in
                            changeMembershipLevel(to: newLevel)
                        }
                    )
                    .padding(.trailing, 20)
                    .padding(.bottom, 20)
                }
            }
        )
    }
    
    // MARK: - Private Methods
    
    /**
     * 更新当前等级显示
     */
    private func updateCurrentLevel() {
        guard let user = dataManager.currentUser else {
            currentLevel = "free"
            return
        }
        currentLevel = user.subscriptionType
    }
    
    /**
     * 切换会员等级
     */
    private func changeMembershipLevel(to level: String) {
        print("🔄 测试页面切换会员等级到: \(level)")
        
        let endDate: Date?
        if level == "free" {
            endDate = nil
        } else {
            endDate = Calendar.current.date(byAdding: .year, value: 1, to: Date())
        }
        
        dataManager.updateSubscription(
            type: level,
            isActive: level != "free",
            startDate: level != "free" ? Date() : nil,
            endDate: endDate,
            productIdentifier: level == "premium" ? "com.ztt.premium.yearly" : (level == "basic" ? "com.ztt.basic.yearly" : nil)
        )
        
        updateCurrentLevel()
        
        let levelName = getLevelDisplayName(level)
        print("✅ 会员等级已切换到: \(levelName)")
    }
    
    /**
     * 获取等级显示名称
     */
    private func getLevelDisplayName(_ level: String) -> String {
        switch level {
        case "free":
            return "免费用户"
        case "basic":
            return "初级会员"
        case "premium":
            return "高级会员"
        default:
            return "未知等级"
        }
    }
    
    /**
     * 获取等级颜色
     */
    private func getLevelColor(_ level: String) -> Color {
        switch level {
        case "premium":
            return Color(hex: "#FFD700")
        case "basic":
            return Color(hex: "#87CEEB")
        default:
            return Color(hex: "#A0A0A0")
        }
    }
}

/**
 * 权限行组件
 */
struct PermissionRow: View {
    let title: String
    let hasPermission: Bool
    let description: String
    
    var body: some View {
        HStack {
            // 权限图标
            Image(systemName: hasPermission ? "checkmark.circle.fill" : "xmark.circle.fill")
                .foregroundColor(hasPermission ? .green : .red)
                .font(.system(size: 20))
            
            // 功能信息
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.primary)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // 状态文字
            Text(hasPermission ? "可用" : "不可用")
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(hasPermission ? .green : .red)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill((hasPermission ? Color.green : Color.red).opacity(0.1))
                )
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color.gray.opacity(0.05))
        )
    }
}

// MARK: - Preview
#Preview {
    MembershipTestView()
}
