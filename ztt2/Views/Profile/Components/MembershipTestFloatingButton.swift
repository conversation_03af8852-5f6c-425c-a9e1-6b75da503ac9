//
//  MembershipTestFloatingButton.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/3.
//

import SwiftUI

/**
 * 会员等级测试悬浮按钮
 * 用于开发阶段测试不同会员等级的功能
 */
struct MembershipTestFloatingButton: View {
    
    // MARK: - Properties
    let currentLevel: String
    let onLevelChanged: (String) -> Void
    
    // MARK: - State
    @State private var showLevelSelector = false
    @State private var isExpanded = false
    
    // MARK: - Constants
    private let buttonSize: CGFloat = 56
    private let iconSize: CGFloat = 24
    
    var body: some View {
        ZStack {
            // 背景遮罩（当展开时）
            if showLevelSelector {
                Color.black.opacity(0.3)
                    .ignoresSafeArea()
                    .onTapGesture {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            showLevelSelector = false
                            isExpanded = false
                        }
                    }
            }
            
            VStack(spacing: 12) {
                // 等级选择按钮（展开时显示）
                if showLevelSelector {
                    VStack(spacing: 8) {
                        // 高级会员按钮
                        LevelButton(
                            level: "premium",
                            displayName: "高级会员",
                            color: Color(hex: "#FFD700"),
                            icon: "crown.fill",
                            isSelected: currentLevel == "premium"
                        ) {
                            selectLevel("premium")
                        }
                        
                        // 初级会员按钮
                        LevelButton(
                            level: "basic",
                            displayName: "初级会员",
                            color: Color(hex: "#87CEEB"),
                            icon: "star.fill",
                            isSelected: currentLevel == "basic"
                        ) {
                            selectLevel("basic")
                        }
                        
                        // 免费用户按钮
                        LevelButton(
                            level: "free",
                            displayName: "免费用户",
                            color: Color(hex: "#A0A0A0"),
                            icon: "person.fill",
                            isSelected: currentLevel == "free"
                        ) {
                            selectLevel("free")
                        }
                    }
                    .transition(.move(edge: .bottom).combined(with: .opacity))
                }
                
                // 主按钮
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        showLevelSelector.toggle()
                        isExpanded.toggle()
                    }
                }) {
                    ZStack {
                        // 背景圆圈
                        Circle()
                            .fill(getCurrentLevelColor())
                            .frame(width: buttonSize, height: buttonSize)
                            .shadow(color: Color.black.opacity(0.2), radius: 8, x: 0, y: 4)
                        
                        // 图标
                        Image(systemName: isExpanded ? "xmark" : getCurrentLevelIcon())
                            .font(.system(size: iconSize, weight: .semibold))
                            .foregroundColor(.white)
                            .rotationEffect(.degrees(isExpanded ? 180 : 0))
                    }
                }
                .scaleEffect(isExpanded ? 1.1 : 1.0)
            }
        }
        .animation(.easeInOut(duration: 0.3), value: showLevelSelector)
    }
    
    // MARK: - Private Methods
    
    /**
     * 选择等级
     */
    private func selectLevel(_ level: String) {
        onLevelChanged(level)
        
        withAnimation(.easeInOut(duration: 0.3)) {
            showLevelSelector = false
            isExpanded = false
        }
        
        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
    }
    
    /**
     * 获取当前等级颜色
     */
    private func getCurrentLevelColor() -> Color {
        switch currentLevel {
        case "premium":
            return Color(hex: "#FFD700")
        case "basic":
            return Color(hex: "#87CEEB")
        default:
            return Color(hex: "#A0A0A0")
        }
    }
    
    /**
     * 获取当前等级图标
     */
    private func getCurrentLevelIcon() -> String {
        switch currentLevel {
        case "premium":
            return "crown.fill"
        case "basic":
            return "star.fill"
        default:
            return "person.fill"
        }
    }
}

/**
 * 等级按钮组件
 */
struct LevelButton: View {
    let level: String
    let displayName: String
    let color: Color
    let icon: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 8) {
                // 图标
                Image(systemName: icon)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.white)
                
                // 文字
                Text(displayName)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white)
                
                // 选中标识
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 16))
                        .foregroundColor(.white)
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(color)
                    .shadow(color: Color.black.opacity(0.15), radius: 4, x: 0, y: 2)
            )
            .scaleEffect(isSelected ? 1.05 : 1.0)
            .opacity(isSelected ? 1.0 : 0.9)
        }
        .buttonStyle(PlainButtonStyle())
    }
}



// MARK: - Preview
#Preview {
    ZStack {
        Color.gray.opacity(0.1)
            .ignoresSafeArea()
        
        VStack {
            Spacer()
            HStack {
                Spacer()
                MembershipTestFloatingButton(
                    currentLevel: "basic",
                    onLevelChanged: { level in
                        print("选择等级: \(level)")
                    }
                )
                .padding()
            }
        }
    }
}
